import Image from "next/image";

import "@/styles/detailPage.styles.css";

import StarRating from "@/app/components/rating";
import {
  extractProductDescription,
  getFormattedPrice,
} from "@/lib/utils/helperFunctions";
import { decimalPlaces } from "@/lib/utils/constants";
import Link from "next/link";
import { ProductReviews } from "@/app/components";

import { ProductDetailProps } from "@/app/components/index.types";

const ProductDetails = ({ product }: ProductDetailProps) => {
  if (!product) return null;

  return (
    <div className={"pro_model_scroll"}>
      {/* Product header section - Keep this with Tailwind */}
      <div className="flex flex-col md:flex-row gap-8 mb-8 pop_box_1">
        {/* Product image */}
        <div className="w-full md:w-1/2">
          <div className="relative aspect-square rounded-lg overflow-hidden">
            <Image
              src={product.image || "/placeholder.svg"}
              alt={product.name}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, 50vw"
              priority
            />
          </div>
        </div>

        {/* Product info */}
        <div className="w-full md:w-1/2 product_detail_page_headings">
          <div>
            <h2 className="text-3xl font-bold mb-2 uppercase">
              {product.name}
            </h2>

            <div className="pop_header">
              {/* Ratings */}
              <div className="flex items-center">
                <StarRating rating={product.rating} />
              </div>

              {/* Price */}
              <h6 className="text-lg font-bold">
                {getFormattedPrice(
                  parseFloat(product?.price) || 0,
                  product?.currency,
                  decimalPlaces
                )}
              </h6>
            </div>
          </div>

          {/* ORDER NOW Link */}
          <div className="mt-7 md:mt-16">
            <Link
              href="/"
              className="font-poppins text-[18px] md:text-[20px] font-normal leading-[20px] tracking-[0em] inline-flex items-center justify-center px-6 py-3 rounded-[2px] capitalize bg-black text-white cursor-pointer shadow-[0px_48px_100px_0px_rgba(17,12,46,0.15)] hover:shadow-lg transition-shadow duration-200 !no-underline"
            >
              ORDER NOW
            </Link>
          </div>
        </div>
      </div>

      {/* Product Description - This is where we apply the anti-Tailwind scope */}
      <div
        dangerouslySetInnerHTML={{
          __html: extractProductDescription(product.description),
        }}
      />

      <ProductReviews />
    </div>
  );
};

export default ProductDetails;
