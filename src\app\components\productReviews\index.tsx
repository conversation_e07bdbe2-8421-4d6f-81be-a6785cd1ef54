"use client";

import { useEffect } from "react";

const ProductReviews = () => {
  useEffect(() => {
    setupAccordion();
  }, []); // Add isReviewModalOpen as a dependency so it re-runs when modal state changes

  // Function to set up accordion functionality
  const setupAccordion = () => {
    const long_data = document.querySelectorAll(".log_des_acc_header");

    // First remove any existing event listeners to prevent duplicates
    long_data.forEach((header) => {
      const oldHeader = header.cloneNode(true);
      header?.parentNode?.replaceChild(oldHeader, header);
    });

    // Now add fresh event listeners
    document.querySelectorAll(".log_des_acc_header").forEach((header) => {
      header.addEventListener("click", function (e) {
        e.preventDefault();
        const targetId = this.getAttribute("data-target");
        if (!targetId) return;

        const targetElement = document.querySelector(targetId);
        const arrow = this.querySelector(".detail_arrow");

        if (!targetElement || !arrow) return;

        // Toggle the current section
        if (targetElement.classList.contains("newshow")) {
          targetElement.classList.remove("newshow");
          arrow.classList.remove("rotate");
        } else {
          // Close all other sections
          document.querySelectorAll(".newcollapse").forEach((collapse) => {
            collapse.classList.remove("newshow");
          });

          document.querySelectorAll(".detail_arrow").forEach((a) => {
            a.classList.remove("rotate");
          });

          // Open this section
          targetElement.classList.add("newshow");
          arrow.classList.add("rotate");
        }
      });
    });

    // Add the exact JS code as provided
    document
      .getElementById("toggle_desc_sub_heading")
      ?.addEventListener("click", () => {
        document
          .getElementById("toggle_desc_sub_paragraph")
          ?.classList.toggle("sub_desc_toggle_hidden");
      });
  };

  return <div>ProductReviews</div>;
};

export default ProductReviews;
