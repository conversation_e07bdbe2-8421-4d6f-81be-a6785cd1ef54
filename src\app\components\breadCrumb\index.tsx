"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

import { generateBreadCrumb, BreadCrumb } from "@/lib/utils/helperFunctions";

// Simple ChevronRight icon component to replace lucide-react dependency
const ChevronRight = ({ className }: { className?: string }) => (
  <svg
    className={className}
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <polyline points="9,18 15,12 9,6"></polyline>
  </svg>
);

type BreadcrumbsProps = {
  items?: BreadCrumb[];
  productName?: string;
  className?: string;
  variant?: "default" | "static";
};

// Main breadcrumb component following project standards
export default function BreadCrumb({
  items,
  productName,
  className = "",
  variant = "default",
}: BreadcrumbsProps) {
  const pathname = usePathname();

  // Generate breadcrumbs if not provided
  const breadcrumbs = items || generateBreadCrumb(pathname, productName);

  // Return null if no breadcrumbs to show
  if (!breadcrumbs || breadcrumbs.length === 0) {
    return null;
  }

  // Static variant following the project's SEO page pattern
  if (variant === "static") {
    return (
      <div className="static_breadcrums_parent">
        <div className="static_container">
          <div className="static_breadcrums">
            {breadcrumbs.map((item, index) => (
              <span key={item.href || index}>
                {item.isCurrent ? (
                  <span>{item.label}</span>
                ) : (
                  <Link href={item.href}>{item.label}</Link>
                )}
                {index < breadcrumbs.length - 1 && <span>/</span>}
              </span>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Default variant with modern styling
  return (
    <nav aria-label="Breadcrumb" className={`text-sm mb-6 ${className}`}>
      <ol className="flex items-center flex-wrap">
        {breadcrumbs.map((item, index) => (
          <li key={item.href || index} className="flex items-center">
            {index > 0 && <ChevronRight className="mx-2 text-gray-400" />}

            {item.isCurrent ? (
              <span className="font-medium text-gray-800" aria-current="page">
                {item.label}
              </span>
            ) : (
              <Link
                href={item.href}
                className="text-gray-600 hover:text-gray-900 hover:underline"
              >
                {item.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
